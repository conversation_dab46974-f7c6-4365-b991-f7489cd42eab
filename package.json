{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Dentabot project", "main": "src/index.js", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.11", "@mui/styled-engine-sc": "^7.1.0", "@react-google-maps/api": "^2.20.6", "@react-oauth/google": "^0.12.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.3", "concurrently": "^9.1.2", "express": "^4.21.2", "openai": "^4.100.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.0", "react-scripts": "^5.0.1", "styled-components": "^6.1.18", "web-vitals": "^2.1.4"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@eslint/js": "^9.27.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "globals": "^16.1.0"}}