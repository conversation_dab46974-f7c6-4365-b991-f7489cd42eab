import React, { useState, useMemo } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box, Typography, useMediaQuery } from '@mui/material';
import { useAuth } from './context/AuthContext';
import config from './config';
import './styles/App.css';
import GlobalSnackbar from './components/GlobalSnackbar';
import AppShell from './components/AppShell';
import { NotificationProvider } from './components/NotificationSystem';
import theme from './theme';

import AppRoutes from './routes';

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const { loading } = useAuth();
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
  const [initialThemeSet, setInitialThemeSet] = useState(false);

  React.useEffect(() => {
    if (!initialThemeSet) {
      setDarkMode(prefersDarkMode);
      setInitialThemeSet(true);
    }
  }, [prefersDarkMode, initialThemeSet]);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const appTheme = useMemo(() => theme(darkMode ? 'dark' : 'light'), [darkMode]);

  if (loading) {
    return null;
  }

  return (
    <ThemeProvider theme={appTheme}>
      <CssBaseline />
      <NotificationProvider>
        <Router>
          <AppShell
            darkMode={darkMode}
            toggleDarkMode={toggleDarkMode}
            showHelpFab={true}
          >
            <AppRoutes />

            {/* Footer */}
            <Box
              component="footer"
              sx={{
                py: { xs: 2, sm: 3 },
                px: { xs: 2, sm: 3 },
                bgcolor: 'background.paper',
                textAlign: 'center',
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                borderTop: '1px solid',
                borderColor: 'divider',
                mt: 'auto',
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  fontSize: 'inherit',
                  color: 'text.secondary',
                  '& a': {
                    color: 'primary.main',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }
                }}
              >
                © {new Date().getFullYear()} {config.app.name} by Zhifei Mi - All rights reserved.
                Contact us at: <a href="mailto:<EMAIL>"><EMAIL></a>
              </Typography>
            </Box>
          </AppShell>
        </Router>
        <GlobalSnackbar />
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;
