import { createTheme } from '@mui/material/styles';

// A custom theme for this app
const theme = (mode) => createTheme({
  palette: {
    mode,
    primary: {
      main: '#1976d2', // Example primary color (Material Blue)
    },
    secondary: {
      main: '#dc004e', // Example secondary color (Material Pink)
    },
    background: {
      default: mode === 'light' ? '#f4f6f8' : '#121212',
      paper: mode === 'light' ? '#ffffff' : '#1e1e1e',
    },
    text: {
      primary: mode === 'light' ? '#172b4d' : '#ffffff',
      secondary: mode === 'light' ? '#6b778c' : '#b0bec5',
    }
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          elevation: 0, // Flat app bar
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none', // No uppercase buttons
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          // boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)', // Subtle shadow
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          // borderRight: 'none',
        }
      }
    }
  },
});

export default theme; 